package order_test

import (
	"context"
	"testing"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	moneyPb "github.com/epifi/be-common/pkg/money"
	usStocksPb "github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/catalog"
	catalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
)

func TestService_GetAccountActivity(t *testing.T) {
	orderSvc, md := getServiceWithMocks(t)
	stocksDao := md.stocksDao
	mockAccountActivityDao := md.accountActivityDao

	type args struct {
		ctx context.Context
		req *orderPb.GetAccountActivityRequest
	}

	actorId := "actor-id"
	executedAt := timestampPb.Now()
	createdAt := timestampPb.Now()
	sampleActivity1 := &orderPb.AccountActivity{
		Id:              "activity-id-1",
		VendorAccountId: "vendor-account-id",
		Type:            orderPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND_SHORT_TERM_CAPITAL_GAIN,
		ActorId:         actorId,
		Symbol:          "AAPL",
		OrderId:         "order-id1",
		NetAmount: &money.Money{
			CurrencyCode: moneyPb.USDCurrencyCode,
			Units:        51,
		},
		ExecutedAt:       executedAt,
		CreatedAt:        createdAt,
		UpdatedAt:        createdAt,
		VendorActivityId: "activity-id-3",
		OrderState:       usStocksPb.OrderState_ORDER_STATE_UNSPECIFIED,
		Vendor:           commonvgpb.Vendor_ALPACA,
	}

	sampleStock1 := &catalogPb.Stock{
		Id:       "catalog-id-1",
		Symbol:   "AAPL",
		Exchange: catalogPb.Exchange_EXCHANGE_NAS,
		StockBasicDetails: &catalogPb.StockBasicDetails{
			Name:           &catalog.CompanyName{ShortName: "Apple", StandardName: "Apple Inc"},
			Description:    nil,
			LogoUrl:        "",
			MarketCapValue: nil,
		},
	}

	activityId := "activity-id-1"
	orderId := "order-id1"
	sampleReq := &orderPb.GetAccountActivityRequest{
		ActivityId: activityId,
	}
	sampleReq1 := &orderPb.GetAccountActivityRequest{
		Identifier: &orderPb.GetAccountActivityRequest_OrderId{
			OrderId: orderId,
		},
	}
	sampleReq2 := &orderPb.GetAccountActivityRequest{
		Identifier: &orderPb.GetAccountActivityRequest_AccountActivityId{
			AccountActivityId: activityId,
		},
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *orderPb.GetAccountActivityResponse
		wantErr        bool
	}{
		{
			name: "getting account activity with usstocks order",
			args: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMockCalls: func() {
				stocksDao.EXPECT().GetAllStocks(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fieldmasks []catalog.StockFieldMask) (map[string]*catalogPb.Stock, error) {
						stocksMap := make(map[string]*catalogPb.Stock)
						stocksMap[sampleStock1.Symbol] = sampleStock1
						return stocksMap, nil
					},
				)
				stocksDao.EXPECT().GetBySymbolAndExchangeId(gomock.Any(), sampleActivity1.GetSymbol(), gomock.Any(), []catalog.StockFieldMask{catalog.StockFieldMask_STOCK_COMPANY_INFO, catalog.StockFieldMask_STOCK_STOCK_BASIC_DETAILS}).Return(sampleStock1, nil)
				mockAccountActivityDao.EXPECT().GetById(gomock.Any(), sampleReq.GetActivityId()).Return(
					sampleActivity1,
					nil,
				)
			},
			want: &orderPb.GetAccountActivityResponse{
				Status:   rpc.StatusOk(),
				Activity: sampleActivity1,
				Stock:    sampleStock1,
			},
		},
		{
			name: "getting account activity with invalid id",
			args: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMockCalls: func() {
				mockAccountActivityDao.EXPECT().GetById(gomock.Any(), sampleReq.GetActivityId()).Return(
					nil,
					epifierrors.ErrRecordNotFound,
				)
			},
			want: &orderPb.GetAccountActivityResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "success - get by stock order id",
			args: args{
				ctx: context.Background(),
				req: sampleReq1,
			},
			setupMockCalls: func() {
				stocksDao.EXPECT().GetAllStocks(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fieldmasks []catalog.StockFieldMask) (map[string]*catalogPb.Stock, error) {
						stocksMap := make(map[string]*catalogPb.Stock)
						stocksMap[symbol] = sampleStock1
						return stocksMap, nil
					})
				stocksDao.EXPECT().GetBySymbolAndExchangeId(gomock.Any(), sampleActivity1.GetSymbol(), sampleStock1.Exchange, []catalog.StockFieldMask{catalog.StockFieldMask_STOCK_COMPANY_INFO, catalog.StockFieldMask_STOCK_STOCK_BASIC_DETAILS}).Return(sampleStock1, nil)
				mockAccountActivityDao.EXPECT().GetByOrderId(gomock.Any(), orderId).Return(sampleActivity1, nil)
			},
			want: &orderPb.GetAccountActivityResponse{
				Status:   rpc.StatusOk(),
				Activity: sampleActivity1,
				Stock:    sampleStock1,
			},
		},
		{
			name: "success - get by account activity identifier id",
			args: args{
				ctx: context.Background(),
				req: sampleReq2,
			},
			setupMockCalls: func() {
				stocksDao.EXPECT().GetAllStocks(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fieldmasks []catalog.StockFieldMask) (map[string]*catalogPb.Stock, error) {
						stocksMap := make(map[string]*catalogPb.Stock)
						stocksMap[sampleStock1.Symbol] = sampleStock1 // sampleStock1.Symbol should be "AAPL"
						return stocksMap, nil
					},
				)
				stocksDao.EXPECT().GetBySymbolAndExchangeId(gomock.Any(), sampleActivity1.GetSymbol(), sampleStock1.Exchange, []catalog.StockFieldMask{catalog.StockFieldMask_STOCK_COMPANY_INFO, catalog.StockFieldMask_STOCK_STOCK_BASIC_DETAILS}).Return(sampleStock1, nil)
				mockAccountActivityDao.EXPECT().GetById(gomock.Any(), activityId).Return(sampleActivity1, nil)
			},
			want: &orderPb.GetAccountActivityResponse{
				Status:   rpc.StatusOk(),
				Activity: sampleActivity1,
				Stock:    sampleStock1,
			},
		},
		{
			name: "error - record not found for account activity id",
			args: args{
				ctx: context.Background(),
				req: &orderPb.GetAccountActivityRequest{
					Identifier: &orderPb.GetAccountActivityRequest_AccountActivityId{
						AccountActivityId: "non-existent-id",
					},
				},
			},
			setupMockCalls: func() {
				mockAccountActivityDao.EXPECT().
					GetById(gomock.Any(), "non-existent-id").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &orderPb.GetAccountActivityResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "error - record not found for stock order id",
			args: args{
				ctx: context.Background(),
				req: &orderPb.GetAccountActivityRequest{
					Identifier: &orderPb.GetAccountActivityRequest_OrderId{
						OrderId: "non-existent-order-id",
					},
				},
			},
			setupMockCalls: func() {
				mockAccountActivityDao.EXPECT().
					GetByOrderId(gomock.Any(), "non-existent-order-id").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &orderPb.GetAccountActivityResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "error - invalid identifier type",
			args: args{
				ctx: context.Background(),
				req: &orderPb.GetAccountActivityRequest{
					Identifier: nil,
				},
			},
			setupMockCalls: func() {},
			want: &orderPb.GetAccountActivityResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := orderSvc.GetAccountActivity(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountActivity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(orderPb.Order{}, "CreatedAt", "UpdatedAt"),
				cmpopts.IgnoreFields(orderPb.AccountActivity{}, "CreatedAt", "UpdatedAt"),
				cmpopts.IgnoreFields(catalog.Stock{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetAccountActivity() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}
